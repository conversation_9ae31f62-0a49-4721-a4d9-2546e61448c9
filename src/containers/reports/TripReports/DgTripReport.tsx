import GravityReport from "../../../components/configurable/compositeComponents/GravityReport";

interface Props {
  application_id: number;
  client_id: number;
  vendor_id: number;
}

const DgTripReport = (props: Props) => {
  let pageId = 25; // default page_id

  if (props.client_id === 16486) {
    pageId = 60;
  } else if (props.vendor_id === 1140) {
    pageId = 28;
  }

  return (
    <div>
      <GravityReport {...props} page_id={pageId} />
    </div>
  );
};

export default DgTripReport;
